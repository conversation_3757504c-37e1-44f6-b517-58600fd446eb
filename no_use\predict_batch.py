import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, precision_score, recall_score, f1_score, roc_curve, auc
import nibabel as nib
from monai.transforms import (
    Compose, ScaleIntensity, Resize, ToTensor
)
from vgg3D_model import vgg

def add_channel_dim(x):
    return x[None, ...]

def plot_confusion_matrix(cm, class_names):
    plt.figure(figsize=(8, 6))
    plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title("Confusion Matrix")
    plt.colorbar()
    tick_marks = np.arange(len(class_names))
    plt.xticks(tick_marks, class_names, rotation=45)
    plt.yticks(tick_marks, class_names)

    # Normalize the confusion matrix
    cm_normalized = cm.astype("float") / cm.sum(axis=1)[:, np.newaxis]
    for i, j in np.ndindex(cm.shape):
        plt.text(j, i, f"{cm[i, j]} ({cm_normalized[i, j]:.2f})",
                 horizontalalignment="center",
                 color="white" if cm[i, j] > cm.max() / 2 else "black")

    plt.tight_layout()
    plt.ylabel("True label")
    plt.xlabel("Predicted label")
    plt.savefig("confusion_matrix.png")
    print("Confusion matrix saved as 'confusion_matrix.png'.")

def plot_roc_curve(true_labels, pred_probs, class_names):
    plt.figure(figsize=(8, 6))
    fpr, tpr, _ = roc_curve(true_labels, pred_probs)
    roc_auc = auc(fpr, tpr)

    plt.plot(fpr, tpr, color='blue', lw=2, label=f"ROC curve (AUC = {roc_auc:.2f})")
    plt.plot([0, 1], [0, 1], color="gray", linestyle="--", lw=2)
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel("False Positive Rate")
    plt.ylabel("True Positive Rate")
    plt.title("Receiver Operating Characteristic")
    plt.legend(loc="lower right")
    plt.savefig("roc_curve.png")
    print("ROC curve saved as 'roc_curve.png'.")

def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using {device} device")

    # 数据预处理
    data_transform = Compose([
        add_channel_dim,  # 添加通道维度
        ScaleIntensity(minv=0.0, maxv=1.0),  # 归一化到[0,1]
        Resize(spatial_size=(64, 128, 128)),  # 调整尺寸
        ToTensor()  # 转换为张量
    ])

    # 读取类别索引
    json_path = './class_indices.json'
    assert os.path.exists(json_path), f"file: '{json_path}' does not exist."
    with open(json_path, "r") as f:
        class_indict = json.load(f)
    print("Loaded class indices:", class_indict)

    # 创建模型
    print("Creating model...")
    model = vgg(model_name="vgg16", in_channels=1, num_classes=len(class_indict), init_weights=True).to(device)
    weights_path = "./3d_vgg.pth"
    assert os.path.exists(weights_path), f"file: '{weights_path}' does not exist."
    print("Loading model weights...")
    model.load_state_dict(torch.load(weights_path, map_location=device))
    model.eval()
    print("Model loaded successfully")

    # 初始化统计变量
    true_labels = []
    pred_labels = []
    image_paths = []
    pred_probs = []

    # 遍历文件夹中的子文件夹
    test_folders = ['HC', 'MCI']  # 测试数据的文件夹
    for label_name in test_folders:
        label_folder = os.path.join('D:\Pythonpro1\yuchuli_hou\smri\data\\test', label_name)
        if not os.path.isdir(label_folder):
            continue

        # 获取真实标签
        try:
            true_label = int(list(class_indict.keys())[list(class_indict.values()).index(label_name)])
        except ValueError:
            print(f"Label '{label_name}' not found in class indices. Skipping...")
            continue

        for img_name in os.listdir(label_folder):
            if not (img_name.endswith('.nii') or img_name.endswith('.nii.gz')):
                continue

            img_path = os.path.join(label_folder, img_name)
            try:
                # 加载NIfTI文件
                img = nib.load(img_path)
                img_data = img.get_fdata(dtype=np.float32)
                
                # 应用转换
                img_data = data_transform(img_data)
                img_data = torch.unsqueeze(img_data, dim=0)

                with torch.no_grad():
                    output = torch.squeeze(model(img_data.to(device))).cpu()
                    predict = torch.softmax(output, dim=0)
                    predict_cla = torch.argmax(predict).item()

                true_labels.append(true_label)
                pred_labels.append(predict_cla)
                image_paths.append(img_path)
                pred_probs.append(predict[1].item())  # 获取阳性类别(MCI)的概率

                print(f"Processing {img_name}: True: {label_name}, Predicted: {list(class_indict.values())[predict_cla]}")

            except Exception as e:
                print(f"Error processing {img_name}: {e}")

    # 计算性能指标
    accuracy = accuracy_score(true_labels, pred_labels)
    precision = precision_score(true_labels, pred_labels)
    recall = recall_score(true_labels, pred_labels)
    f1 = f1_score(true_labels, pred_labels)

    print("\nModel Performance Metrics:")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    print(f"F1-Score: {f1:.4f}")

    # 分类报告
    print("\nClassification Report:")
    print(classification_report(true_labels, pred_labels, target_names=list(class_indict.values())))

    # 混淆矩阵
    cm = confusion_matrix(true_labels, pred_labels)
    print("\nConfusion Matrix:")
    print(cm)
    plot_confusion_matrix(cm, list(class_indict.values()))

    # 绘制 ROC 曲线
    plot_roc_curve(true_labels, pred_probs, list(class_indict.values()))

    # 保存结果到文件
    results = [{"image": path, "true_label": true, "predicted_label": pred}
               for path, true, pred in zip(image_paths, true_labels, pred_labels)]
    with open("prediction_results.json", "w") as f:
        json.dump(results, f, indent=4)
    print("Prediction results saved to 'prediction_results.json'.")

if __name__ == '__main__':
    main()
