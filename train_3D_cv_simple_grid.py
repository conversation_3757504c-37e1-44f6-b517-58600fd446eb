import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from tqdm import tqdm
from sklearn.model_selection import <PERSON><PERSON>old
import nibabel as nib
from torch.utils.data import Dataset, DataLoader, Subset
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip, 
    RandZoom, Resize, ToTensor
)
from vgg3D_model import vgg
from itertools import product
import time

class NiftiDataset(Dataset):
    def __init__(self, data_dirs, classes, transform=None):
        self.data = []
        self.transform = transform
        
        # 遍历每个数据目录（HC和MCI）
        for class_idx, class_name in enumerate(classes):
            class_dir = data_dirs[class_name]
            if not os.path.isdir(class_dir):
                continue
                
            # 收集所有NIfTI文件
            for file_name in os.listdir(class_dir):
                if file_name.endswith('.nii') or file_name.endswith('.nii.gz'):
                    self.data.append({
                        'image_path': os.path.join(class_dir, file_name),
                        'label': class_idx
                    })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载NIfTI文件
        img = nib.load(sample['image_path'])
        image_data = img.get_fdata(dtype=np.float32)
        
        # 应用变换
        if self.transform:
            image_data = self.transform(image_data)
            
        return image_data, sample['label']

def add_channel_dim(x):
    return x[None, ...]

def train_fold_simple(train_loader, val_loader, device, fold_num, lr, weight_decay, optimizer_name, epochs=20):
    """
    简化的训练函数，只调整关键超参数
    """
    # 创建模型
    net = vgg(model_name="vgg16", in_channels=1, num_classes=2, init_weights=True)
    net.to(device)
    
    # 定义损失函数和优化器
    loss_function = nn.CrossEntropyLoss()
    
    if optimizer_name == 'Adam':
        optimizer = optim.Adam(net.parameters(), lr=lr, weight_decay=weight_decay)
    else:
        optimizer = optim.AdamW(net.parameters(), lr=lr, weight_decay=weight_decay)
    
    best_acc = 0.0
    
    for epoch in range(epochs):
        # 训练阶段
        net.train()
        running_loss = 0.0
        
        for data in train_loader:
            images, labels = data
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            logits = net(images)
            loss = loss_function(logits, labels)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
        
        # 验证阶段
        net.eval()
        acc = 0.0
        val_num = len(val_loader.dataset)
        
        with torch.no_grad():
            for val_data in val_loader:
                val_images, val_labels = val_data
                val_images, val_labels = val_images.to(device), val_labels.to(device)
                
                outputs = net(val_images)
                predict_y = torch.max(outputs, dim=1)[1]
                acc += torch.eq(predict_y, val_labels).sum().item()
        
        val_accurate = acc / val_num
        
        if val_accurate > best_acc:
            best_acc = val_accurate
    
    return best_acc

def simple_grid_search():
    """
    简化的网格搜索，只搜索最重要的超参数
    """
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using {device} device.")
    
    # 定义数据变换
    train_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        RandRotate(range_x=15, range_y=15, range_z=15, prob=0.5),
        RandFlip(prob=0.5, spatial_axis=0),
        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    val_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    # 数据目录设置
    data_dirs = {
        'HC': './HC',
        'MCI': './MCI'
    }
    classes = ['HC', 'MCI']
    
    # 创建完整数据集
    full_dataset = NiftiDataset(data_dirs, classes, transform=None)
    print(f"Total samples: {len(full_dataset)}")
    
    # 简化的超参数网格（只搜索最重要的参数）
    learning_rates = [0.0001, 0.0005, 0.001]
    weight_decays = [0.0, 0.0001, 0.001]
    optimizers = ['Adam', 'AdamW']
    batch_size = 1  # 固定批次大小
    epochs = 20     # 减少训练轮数以加快搜索
    
    print(f"\n=== Simple Grid Search ===")
    print(f"Testing {len(learning_rates)} learning rates × {len(weight_decays)} weight decays × {len(optimizers)} optimizers")
    print(f"Total combinations: {len(learning_rates) * len(weight_decays) * len(optimizers)}")
    
    best_score = 0.0
    best_params = None
    results = []
    
    # 设置交叉验证
    kfold = KFold(n_splits=3, shuffle=True, random_state=42)
    
    combination_count = 0
    total_combinations = len(learning_rates) * len(weight_decays) * len(optimizers)
    
    for lr in learning_rates:
        for wd in weight_decays:
            for opt in optimizers:
                combination_count += 1
                print(f"\n--- Testing combination {combination_count}/{total_combinations} ---")
                print(f"Learning Rate: {lr}, Weight Decay: {wd}, Optimizer: {opt}")
                
                fold_scores = []
                start_time = time.time()
                
                for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset)):
                    # 创建训练和验证子集
                    train_subset = Subset(full_dataset, train_idx)
                    val_subset = Subset(full_dataset, val_idx)
                    
                    # 为子集设置不同的变换
                    train_subset.dataset.transform = train_transform
                    val_subset.dataset.transform = val_transform
                    
                    # 创建数据加载器
                    train_loader = DataLoader(train_subset, batch_size=batch_size, 
                                            shuffle=True, num_workers=0)
                    val_loader = DataLoader(val_subset, batch_size=batch_size, 
                                          shuffle=False, num_workers=0)
                    
                    # 训练当前折
                    fold_acc = train_fold_simple(train_loader, val_loader, device, 
                                               fold + 1, lr, wd, opt, epochs)
                    fold_scores.append(fold_acc)
                    
                    print(f"  Fold {fold + 1}: {fold_acc:.4f}")
                
                # 计算平均分数
                mean_score = np.mean(fold_scores)
                std_score = np.std(fold_scores)
                elapsed_time = time.time() - start_time
                
                print(f"  Mean CV Score: {mean_score:.4f} ± {std_score:.4f}")
                print(f"  Time elapsed: {elapsed_time:.2f} seconds")
                
                # 存储结果
                result = {
                    'learning_rate': lr,
                    'weight_decay': wd,
                    'optimizer': opt,
                    'mean_score': mean_score,
                    'std_score': std_score,
                    'fold_scores': fold_scores,
                    'time_elapsed': elapsed_time
                }
                results.append(result)
                
                # 更新最佳参数
                if mean_score > best_score:
                    best_score = mean_score
                    best_params = {
                        'learning_rate': lr,
                        'weight_decay': wd,
                        'optimizer': opt,
                        'batch_size': batch_size,
                        'epochs': epochs
                    }
                    print(f"  *** New best score: {best_score:.4f} ***")
    
    print(f"\n=== Grid Search Completed ===")
    print(f"Best parameters: {best_params}")
    print(f"Best CV score: {best_score:.4f}")
    
    # 保存结果
    grid_search_results = {
        'best_params': best_params,
        'best_score': best_score,
        'all_results': results
    }
    
    with open('simple_grid_search_results.json', 'w') as f:
        json.dump(grid_search_results, f, indent=2)
    
    print("Grid search results saved to 'simple_grid_search_results.json'")
    
    return best_params

if __name__ == '__main__':
    best_params = simple_grid_search()
    print(f"\nRecommended hyperparameters: {best_params}")
