import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from tqdm import tqdm
from sklearn.model_selection import <PERSON><PERSON>old
import nibabel as nib
from torch.utils.data import Dataset, DataLoader, Subset
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip, 
    RandZoom, Resize, ToTensor
)
from vgg3D_model import vgg
from resnet3D_model import resnet3d
from itertools import product
import time

class NiftiDataset(Dataset):
    def __init__(self, data_dirs, classes, transform=None):
        self.data = []
        self.transform = transform
        
        # 遍历每个数据目录（HC和MCI）
        for class_idx, class_name in enumerate(classes):
            class_dir = data_dirs[class_name]
            if not os.path.isdir(class_dir):
                continue
                
            # 收集所有NIfTI文件
            for file_name in os.listdir(class_dir):
                if file_name.endswith('.nii') or file_name.endswith('.nii.gz'):
                    self.data.append({
                        'image_path': os.path.join(class_dir, file_name),
                        'label': class_idx
                    })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载NIfTI文件
        img = nib.load(sample['image_path'])
        image_data = img.get_fdata(dtype=np.float32)
        
        # 应用变换
        if self.transform:
            image_data = self.transform(image_data)
            
        return image_data, sample['label']

def add_channel_dim(x):
    return x[None, ...]

def train_fold(train_loader, val_loader, device, fold_num, hyperparams, verbose=True):
    """
    训练单个折叠
    Args:
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        device: 设备
        fold_num: 折叠编号
        hyperparams: 超参数字典
        verbose: 是否显示详细信息
    """
    # 创建模型
    if hyperparams['model_name'].startswith('resnet'):
        net = resnet3d(model_name=hyperparams['model_name'], in_channels=1, num_classes=2, init_weights=True)
    else:
        net = vgg(model_name=hyperparams['model_name'], in_channels=1, num_classes=2, init_weights=True)
    net.to(device)
    
    # 定义损失函数和优化器
    loss_function = nn.CrossEntropyLoss()
    
    if hyperparams['optimizer'] == 'Adam':
        optimizer = optim.Adam(net.parameters(), lr=hyperparams['learning_rate'], 
                              weight_decay=hyperparams['weight_decay'])
    elif hyperparams['optimizer'] == 'SGD':
        optimizer = optim.SGD(net.parameters(), lr=hyperparams['learning_rate'], 
                             momentum=0.9, weight_decay=hyperparams['weight_decay'])
    else:
        optimizer = optim.AdamW(net.parameters(), lr=hyperparams['learning_rate'], 
                               weight_decay=hyperparams['weight_decay'])
    
    # 学习率调度器
    if hyperparams['scheduler'] == 'StepLR':
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
    elif hyperparams['scheduler'] == 'CosineAnnealingLR':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=hyperparams['epochs'])
    else:
        scheduler = None
    
    # 训练参数
    epochs = hyperparams['epochs']
    best_acc = 0.0
    
    if verbose:
        print(f"\n=== Training Fold {fold_num} with hyperparams: {hyperparams} ===")
    
    for epoch in range(epochs):
        # 训练阶段
        net.train()
        running_loss = 0.0
        
        if verbose:
            train_bar = tqdm(train_loader, file=sys.stdout, desc=f"Fold {fold_num} Train")
        else:
            train_bar = train_loader
        
        for data in train_bar:
            images, labels = data
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            logits = net(images)
            loss = loss_function(logits, labels)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
            if verbose:
                train_bar.set_postfix(loss=f"{loss.item():.3f}")
        
        # 更新学习率
        if scheduler:
            scheduler.step()
        
        # 验证阶段
        net.eval()
        acc = 0.0
        val_num = len(val_loader.dataset)
        
        with torch.no_grad():
            if verbose:
                val_bar = tqdm(val_loader, file=sys.stdout, desc=f"Fold {fold_num} Val")
            else:
                val_bar = val_loader
                
            for val_data in val_bar:
                val_images, val_labels = val_data
                val_images, val_labels = val_images.to(device), val_labels.to(device)
                
                outputs = net(val_images)
                predict_y = torch.max(outputs, dim=1)[1]
                acc += torch.eq(predict_y, val_labels).sum().item()
        
        val_accurate = acc / val_num
        avg_loss = running_loss / len(train_loader)
        
        if verbose:
            print(f'Fold {fold_num} [epoch {epoch+1}] train_loss: {avg_loss:.3f}  val_accuracy: {val_accurate:.3f}')
        
        # 保存最佳模型
        if val_accurate > best_acc:
            best_acc = val_accurate
            if verbose:
                torch.save(net.state_dict(), f'./3d_vgg_fold_{fold_num}.pth')
    
    return best_acc

def grid_search_cv(full_dataset, train_transform, val_transform, device, param_grid, cv_folds=3):
    """
    执行网格搜索交叉验证
    Args:
        full_dataset: 完整数据集
        train_transform: 训练数据变换
        val_transform: 验证数据变换
        device: 设备
        param_grid: 超参数网格
        cv_folds: 交叉验证折数
    Returns:
        best_params: 最佳超参数
        results: 所有结果
    """
    print("=== Starting Grid Search Cross Validation ===")
    
    # 生成所有超参数组合
    param_names = list(param_grid.keys())
    param_values = list(param_grid.values())
    param_combinations = list(product(*param_values))
    
    print(f"Total parameter combinations to test: {len(param_combinations)}")
    
    # 存储结果
    results = []
    best_score = 0.0
    best_params = None
    
    # 设置交叉验证
    kfold = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
    
    for i, param_combo in enumerate(param_combinations):
        # 创建当前超参数字典
        current_params = dict(zip(param_names, param_combo))
        
        print(f"\n--- Testing combination {i+1}/{len(param_combinations)} ---")
        print(f"Parameters: {current_params}")
        
        # 存储当前组合的所有折叠结果
        fold_scores = []
        start_time = time.time()
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset)):
            # 创建训练和验证子集
            train_subset = Subset(full_dataset, train_idx)
            val_subset = Subset(full_dataset, val_idx)
            
            # 为子集设置不同的变换
            train_subset.dataset.transform = train_transform
            val_subset.dataset.transform = val_transform
            
            # 创建数据加载器
            train_loader = DataLoader(train_subset, batch_size=current_params['batch_size'], 
                                    shuffle=True, num_workers=0)
            val_loader = DataLoader(val_subset, batch_size=current_params['batch_size'], 
                                  shuffle=False, num_workers=0)
            
            # 训练当前折
            fold_acc = train_fold(train_loader, val_loader, device, fold + 1, 
                                current_params, verbose=False)
            fold_scores.append(fold_acc)
            
            print(f"  Fold {fold + 1}: {fold_acc:.4f}")
        
        # 计算平均分数
        mean_score = np.mean(fold_scores)
        std_score = np.std(fold_scores)
        elapsed_time = time.time() - start_time
        
        print(f"  Mean CV Score: {mean_score:.4f} ± {std_score:.4f}")
        print(f"  Time elapsed: {elapsed_time:.2f} seconds")
        
        # 存储结果
        result = {
            'params': current_params.copy(),
            'mean_score': mean_score,
            'std_score': std_score,
            'fold_scores': fold_scores,
            'time_elapsed': elapsed_time
        }
        results.append(result)
        
        # 更新最佳参数
        if mean_score > best_score:
            best_score = mean_score
            best_params = current_params.copy()
            print(f"  *** New best score: {best_score:.4f} ***")
    
    print(f"\n=== Grid Search Completed ===")
    print(f"Best parameters: {best_params}")
    print(f"Best CV score: {best_score:.4f}")
    
    return best_params, results

def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using {device} device.")

    # 定义数据变换
    train_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        RandRotate(range_x=15, range_y=15, range_z=15, prob=0.5),
        RandFlip(prob=0.5, spatial_axis=0),
        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])

    val_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])

    # 数据目录设置
    data_dirs = {
        'HC': './HC',
        'MCI': './MCI'
    }
    classes = ['HC', 'MCI']

    # 创建完整数据集
    full_dataset = NiftiDataset(data_dirs, classes, transform=None)
    print(f"Total samples: {len(full_dataset)}")

    # 定义超参数网格
    param_grid = {
        'learning_rate': [0.0001, 0.0005, 0.001],
        'batch_size': [1, 2],
        'optimizer': ['Adam', 'SGD'],
        'weight_decay': [0.0, 0.0001, 0.001],
        'epochs': [20, 30],
        'model_name': ['vgg16', 'resnet18', 'resnet34'],  # 添加ResNet模型
        'scheduler': [None, 'StepLR', 'CosineAnnealingLR']
    }

    # 询问用户是否进行网格搜索
    print("\nChoose training mode:")
    print("1. Grid Search (find best hyperparameters)")
    print("2. Standard Training (use default hyperparameters)")

    try:
        choice = input("Enter your choice (1 or 2): ").strip()
    except:
        choice = "2"  # 默认选择标准训练

    if choice == "1":
        # 执行网格搜索
        print("\n=== Starting Grid Search ===")
        best_params, grid_results = grid_search_cv(
            full_dataset, train_transform, val_transform, device, param_grid, cv_folds=3
        )

        # 保存网格搜索结果
        grid_search_results = {
            'best_params': best_params,
            'all_results': grid_results
        }

        with open('grid_search_results.json', 'w') as f:
            json.dump(grid_search_results, f, indent=2, default=str)

        print(f"\nGrid search results saved to 'grid_search_results.json'")

        # 使用最佳参数进行最终训练
        print(f"\n=== Final Training with Best Parameters ===")
        print(f"Best parameters: {best_params}")

        final_fold_accuracies = []
        kfold = KFold(n_splits=3, shuffle=True, random_state=42)

        for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset)):
            print(f"\nFinal Training - Fold {fold + 1}/{3}")
            print(f"Train samples: {len(train_idx)}, Val samples: {len(val_idx)}")

            # 创建训练和验证子集
            train_subset = Subset(full_dataset, train_idx)
            val_subset = Subset(full_dataset, val_idx)

            # 为子集设置不同的变换
            train_subset.dataset.transform = train_transform
            val_subset.dataset.transform = val_transform

            # 创建数据加载器
            train_loader = DataLoader(train_subset, batch_size=best_params['batch_size'],
                                    shuffle=True, num_workers=0)
            val_loader = DataLoader(val_subset, batch_size=best_params['batch_size'],
                                  shuffle=False, num_workers=0)

            # 训练当前折
            fold_acc = train_fold(train_loader, val_loader, device, fold + 1, best_params, verbose=True)
            final_fold_accuracies.append(fold_acc)

            print(f"Final Fold {fold + 1} best accuracy: {fold_acc:.4f}")

        fold_accuracies = final_fold_accuracies

    else:
        # 标准训练（使用默认超参数）
        print("\n=== Standard Training with Default Parameters ===")

        # 默认超参数
        default_params = {
            'learning_rate': 0.0001,
            'batch_size': 1,
            'optimizer': 'Adam',
            'weight_decay': 0.0,
            'epochs': 30,
            'model_name': 'vgg16',
            'scheduler': None
        }

        print(f"Using default parameters: {default_params}")

        # 三折交叉验证
        kfold = KFold(n_splits=3, shuffle=True, random_state=42)
        fold_accuracies = []

        for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset)):
            print(f"\nFold {fold + 1}/{3}")
            print(f"Train samples: {len(train_idx)}, Val samples: {len(val_idx)}")

            # 创建训练和验证子集
            train_subset = Subset(full_dataset, train_idx)
            val_subset = Subset(full_dataset, val_idx)

            # 为子集设置不同的变换
            train_subset.dataset.transform = train_transform
            val_subset.dataset.transform = val_transform

            # 创建数据加载器
            train_loader = DataLoader(train_subset, batch_size=default_params['batch_size'],
                                    shuffle=True, num_workers=0)
            val_loader = DataLoader(val_subset, batch_size=default_params['batch_size'],
                                  shuffle=False, num_workers=0)

            # 训练当前折
            fold_acc = train_fold(train_loader, val_loader, device, fold + 1, default_params, verbose=True)
            fold_accuracies.append(fold_acc)

            print(f"Fold {fold + 1} best accuracy: {fold_acc:.4f}")

    # 输出最终交叉验证结果
    mean_acc = np.mean(fold_accuracies)
    std_acc = np.std(fold_accuracies)

    print(f"\n=== Final Cross Validation Results ===")
    for i, acc in enumerate(fold_accuracies):
        print(f"Fold {i+1}: {acc:.4f}")
    print(f"Mean Accuracy: {mean_acc:.4f} ± {std_acc:.4f}")

    # 保存最终结果
    final_results = {
        'fold_accuracies': fold_accuracies,
        'mean_accuracy': mean_acc,
        'std_accuracy': std_acc,
        'training_mode': 'grid_search' if choice == "1" else 'standard'
    }

    if choice == "1":
        final_results['best_hyperparameters'] = best_params

    with open('final_cv_results.json', 'w') as f:
        json.dump(final_results, f, indent=2)

    print("Training completed! Results saved to 'final_cv_results.json'")

if __name__ == '__main__':
    main()
