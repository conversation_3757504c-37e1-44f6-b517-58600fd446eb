import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from tqdm import tqdm
from sklearn.model_selection import <PERSON><PERSON>old
import nibabel as nib
from torch.utils.data import Dataset, DataLoader, Subset
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip, 
    RandZoom, Resize, ToTensor
)
from model import simple_cnn3d

class NiftiDataset(Dataset):
    def __init__(self, data_dirs, classes, transform=None):
        self.data = []
        self.transform = transform
        
        # 遍历每个数据目录（HC和MCI）
        for class_idx, class_name in enumerate(classes):
            class_dir = data_dirs[class_name]
            if not os.path.isdir(class_dir):
                continue
                
            # 收集所有NIfTI文件
            for file_name in os.listdir(class_dir):
                if file_name.endswith('.nii') or file_name.endswith('.nii.gz'):
                    self.data.append({
                        'image_path': os.path.join(class_dir, file_name),
                        'label': class_idx
                    })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载NIfTI文件
        img = nib.load(sample['image_path'])
        image_data = img.get_fdata(dtype=np.float32)
        
        # 应用变换
        if self.transform:
            image_data = self.transform(image_data)
            
        return image_data, sample['label']

def add_channel_dim(x):
    return x[None, ...]

def train_fold(train_loader, val_loader, device, fold_num,model_type="simple"):
    # 创建模型
    net = simple_cnn3d(model_type=model_type, in_channels=1, num_classes=2, dropout_rate=0.5)
    net.to(device)
    
    # 定义损失函数和优化器
    loss_function = nn.CrossEntropyLoss()
    optimizer = optim.Adam(net.parameters(), lr=0.001, weight_decay=0.01)
    
    # 训练参数
    epochs = 10
    best_acc = 0.0
    
    print(f"\n=== Training Fold {fold_num} ===")
    
    for epoch in range(epochs):
        # 训练阶段
        net.train()
        running_loss = 0.0
        train_bar = tqdm(train_loader, file=sys.stdout, desc=f"Fold {fold_num} Train")
        
        for step, data in enumerate(train_bar):
            images, labels = data
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            logits = net(images)
            loss = loss_function(logits, labels)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
            train_bar.set_postfix(loss=f"{loss.item():.3f}")
        
        # 验证阶段
        net.eval()
        acc = 0.0
        val_num = len(val_loader.dataset)
        
        with torch.no_grad():
            val_bar = tqdm(val_loader, file=sys.stdout, desc=f"Fold {fold_num} Val")
            for val_data in val_bar:
                val_images, val_labels = val_data
                val_images, val_labels = val_images.to(device), val_labels.to(device)
                
                outputs = net(val_images)
                predict_y = torch.max(outputs, dim=1)[1]
                acc += torch.eq(predict_y, val_labels).sum().item()
        
        val_accurate = acc / val_num
        avg_loss = running_loss / len(train_loader)
        
        print(f'Fold {fold_num} [epoch {epoch+1}] train_loss: {avg_loss:.3f}  val_accuracy: {val_accurate:.3f}')
        
        # 保存最佳模型
        if val_accurate > best_acc:
            best_acc = val_accurate
            torch.save(net.state_dict(), f'./3d_vgg_fold_{fold_num}.pth')
    
    return best_acc

def main():
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using {device} device.")
    
    # 定义数据变换
    train_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        RandRotate(range_x=15, range_y=15, range_z=15, prob=0.5),
        RandFlip(prob=0.5, spatial_axis=0),
        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    val_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    # 数据目录设置
    data_dirs = {
        'HC': './HC',
        'MCI': './MCI'
    }
    classes = ['HC', 'MCI']
    
    # 创建完整数据集
    full_dataset = NiftiDataset(data_dirs, classes, transform=None)
    print(f"Total samples: {len(full_dataset)}")
    
    # 三折交叉验证
    kfold = KFold(n_splits=10, shuffle=True, random_state=42)
    fold_accuracies = []
    
    for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset)):
        print(f"\nFold {fold + 1}/{10}")
        print(f"Train samples: {len(train_idx)}, Val samples: {len(val_idx)}")
        
        # 创建训练和验证子集
        train_subset = Subset(full_dataset, train_idx)
        val_subset = Subset(full_dataset, val_idx)
        
        # 为子集设置不同的变换
        train_subset.dataset.transform = train_transform
        val_subset.dataset.transform = val_transform
        
        # 创建数据加载器
        batch_size = 4
        train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True, num_workers=0)
        val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False, num_workers=0)
        
        # 训练当前折
        fold_acc = train_fold(train_loader, val_loader, device, fold + 1)
        fold_accuracies.append(fold_acc)
        
        print(f"Fold {fold + 1} best accuracy: {fold_acc:.4f}")
    
    # 输出交叉验证结果
    mean_acc = np.mean(fold_accuracies)
    std_acc = np.std(fold_accuracies)
    
    print(f"\n=== Cross Validation Results ===")
    for i, acc in enumerate(fold_accuracies):
        print(f"Fold {i+1}: {acc:.4f}")
    print(f"Mean Accuracy: {mean_acc:.4f} ± {std_acc:.4f}")
    
    # 保存结果
    results = {
        'fold_accuracies': fold_accuracies,
        'mean_accuracy': mean_acc,
        'std_accuracy': std_acc
    }
    
    with open('cv_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("Cross validation completed!")

if __name__ == '__main__':
    main()