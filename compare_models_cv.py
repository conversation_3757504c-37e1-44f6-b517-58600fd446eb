import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from tqdm import tqdm
from sklearn.model_selection import <PERSON><PERSON>old
import nibabel as nib
from torch.utils.data import Dataset, DataLoader, Subset
from monai.transforms import (
    Compose, ScaleIntensity, RandRotate, RandFlip, 
    RandZoom, Resize, ToTensor
)
from vgg3D_model import vgg
from resnet3D_model import resnet3d
import time

class NiftiDataset(Dataset):
    def __init__(self, data_dirs, classes, transform=None):
        self.data = []
        self.transform = transform
        
        # 遍历每个数据目录（HC和MCI）
        for class_idx, class_name in enumerate(classes):
            class_dir = data_dirs[class_name]
            if not os.path.isdir(class_dir):
                continue
                
            # 收集所有NIfTI文件
            for file_name in os.listdir(class_dir):
                if file_name.endswith('.nii') or file_name.endswith('.nii.gz'):
                    self.data.append({
                        'image_path': os.path.join(class_dir, file_name),
                        'label': class_idx
                    })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # 加载NIfTI文件
        img = nib.load(sample['image_path'])
        image_data = img.get_fdata(dtype=np.float32)
        
        # 应用变换
        if self.transform:
            image_data = self.transform(image_data)
            
        return image_data, sample['label']

def add_channel_dim(x):
    return x[None, ...]

def train_model(train_loader, val_loader, device, model_name, fold_num, epochs=20):
    """
    训练指定模型
    """
    # 创建模型
    if model_name.startswith('resnet'):
        net = resnet3d(model_name=model_name, in_channels=1, num_classes=2, init_weights=True)
    else:
        net = vgg(model_name=model_name, in_channels=1, num_classes=2, init_weights=True)
    
    net.to(device)
    
    # 计算模型参数
    total_params = sum(p.numel() for p in net.parameters())
    trainable_params = sum(p.numel() for p in net.parameters() if p.requires_grad)
    
    # 定义损失函数和优化器
    loss_function = nn.CrossEntropyLoss()
    optimizer = optim.Adam(net.parameters(), lr=0.0001, weight_decay=0.0001)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
    
    best_acc = 0.0
    training_time = 0
    
    print(f"  Training {model_name} - Fold {fold_num}")
    print(f"  Parameters: {total_params:,} (trainable: {trainable_params:,})")
    
    start_time = time.time()
    
    for epoch in range(epochs):
        # 训练阶段
        net.train()
        running_loss = 0.0
        
        for data in train_loader:
            images, labels = data
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            logits = net(images)
            loss = loss_function(logits, labels)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
        
        scheduler.step()
        
        # 验证阶段
        net.eval()
        acc = 0.0
        val_num = len(val_loader.dataset)
        
        with torch.no_grad():
            for val_data in val_loader:
                val_images, val_labels = val_data
                val_images, val_labels = val_images.to(device), val_labels.to(device)
                
                outputs = net(val_images)
                predict_y = torch.max(outputs, dim=1)[1]
                acc += torch.eq(predict_y, val_labels).sum().item()
        
        val_accurate = acc / val_num
        
        if val_accurate > best_acc:
            best_acc = val_accurate
    
    training_time = time.time() - start_time
    
    return {
        'best_accuracy': best_acc,
        'total_params': total_params,
        'trainable_params': trainable_params,
        'training_time': training_time
    }

def compare_models():
    """
    比较不同模型的性能
    """
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    print(f"Using {device} device.")
    
    # 定义要比较的模型
    models_to_compare = [
        'vgg16',
        'resnet18', 
        'resnet34',
        'resnet50'
    ]
    
    print(f"\nComparing models: {models_to_compare}")
    
    # 定义数据变换
    train_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        RandRotate(range_x=15, range_y=15, range_z=15, prob=0.5),
        RandFlip(prob=0.5, spatial_axis=0),
        RandZoom(min_zoom=0.9, max_zoom=1.1, prob=0.5),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    val_transform = Compose([
        add_channel_dim,
        ScaleIntensity(minv=0.0, maxv=1.0),
        Resize(spatial_size=(64, 128, 128)),
        ToTensor()
    ])
    
    # 数据目录设置
    data_dirs = {
        'HC': './HC',
        'MCI': './MCI'
    }
    classes = ['HC', 'MCI']
    
    # 创建完整数据集
    full_dataset = NiftiDataset(data_dirs, classes, transform=None)
    print(f"Total samples: {len(full_dataset)}")
    
    # 设置交叉验证
    kfold = KFold(n_splits=3, shuffle=True, random_state=42)
    
    # 存储所有结果
    all_results = {}
    
    for model_name in models_to_compare:
        print(f"\n{'='*50}")
        print(f"Testing {model_name.upper()}")
        print(f"{'='*50}")
        
        model_results = {
            'fold_accuracies': [],
            'fold_times': [],
            'total_params': 0,
            'trainable_params': 0
        }
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(full_dataset)):
            print(f"\nFold {fold + 1}/{3}")
            print(f"Train samples: {len(train_idx)}, Val samples: {len(val_idx)}")
            
            # 创建训练和验证子集
            train_subset = Subset(full_dataset, train_idx)
            val_subset = Subset(full_dataset, val_idx)
            
            # 为子集设置不同的变换
            train_subset.dataset.transform = train_transform
            val_subset.dataset.transform = val_transform
            
            # 创建数据加载器
            batch_size = 1
            train_loader = DataLoader(train_subset, batch_size=batch_size, 
                                    shuffle=True, num_workers=0)
            val_loader = DataLoader(val_subset, batch_size=batch_size, 
                                  shuffle=False, num_workers=0)
            
            # 训练模型
            fold_result = train_model(train_loader, val_loader, device, 
                                    model_name, fold + 1, epochs=20)
            
            model_results['fold_accuracies'].append(fold_result['best_accuracy'])
            model_results['fold_times'].append(fold_result['training_time'])
            model_results['total_params'] = fold_result['total_params']
            model_results['trainable_params'] = fold_result['trainable_params']
            
            print(f"  Fold {fold + 1} accuracy: {fold_result['best_accuracy']:.4f}")
            print(f"  Training time: {fold_result['training_time']:.2f} seconds")
        
        # 计算统计信息
        mean_acc = np.mean(model_results['fold_accuracies'])
        std_acc = np.std(model_results['fold_accuracies'])
        mean_time = np.mean(model_results['fold_times'])
        
        model_results['mean_accuracy'] = mean_acc
        model_results['std_accuracy'] = std_acc
        model_results['mean_training_time'] = mean_time
        
        all_results[model_name] = model_results
        
        print(f"\n{model_name.upper()} Results:")
        print(f"  Mean Accuracy: {mean_acc:.4f} ± {std_acc:.4f}")
        print(f"  Mean Training Time: {mean_time:.2f} seconds")
        print(f"  Parameters: {model_results['total_params']:,}")
    
    # 输出比较结果
    print(f"\n{'='*80}")
    print("MODEL COMPARISON SUMMARY")
    print(f"{'='*80}")
    
    print(f"{'Model':<12} {'Accuracy':<15} {'Std':<8} {'Params':<12} {'Time(s)':<10}")
    print(f"{'-'*80}")
    
    for model_name, results in all_results.items():
        print(f"{model_name:<12} "
              f"{results['mean_accuracy']:.4f}<11 "
              f"±{results['std_accuracy']:.4f}<4 "
              f"{results['total_params']:,}<12 "
              f"{results['mean_training_time']:.1f}")
    
    # 找出最佳模型
    best_model = max(all_results.items(), key=lambda x: x[1]['mean_accuracy'])
    print(f"\nBest Model: {best_model[0].upper()}")
    print(f"Best Accuracy: {best_model[1]['mean_accuracy']:.4f} ± {best_model[1]['std_accuracy']:.4f}")
    
    # 保存结果
    with open('model_comparison_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\nDetailed results saved to 'model_comparison_results.json'")
    
    return all_results

if __name__ == '__main__':
    results = compare_models()
    print("\nModel comparison completed!")
