import torch.nn as nn
import torch

# official pretrain weights
model_urls = {
    'vgg11': 'https://download.pytorch.org/models/vgg11-bbd30ac9.pth',
    'vgg13': 'https://download.pytorch.org/models/vgg13-c768596a.pth',
    'vgg16': 'https://download.pytorch.org/models/vgg16-397923af.pth',
    'vgg19': 'https://download.pytorch.org/models/vgg19-dcbb9e9d.pth'
}


class VGG(nn.Module):
    def __init__(self, features, num_classes=1000, init_weights=True):
        super(VGG, self).__init__()
        self.features = features
        # 添加自适应平均池化层，统一特征图尺寸为(4,4,4)
        self.adaptive_avg_pool = nn.AdaptiveAvgPool3d((4, 4, 4))
        # 调整全连接层输入维度为512*4*4*4
        self.classifier = nn.Sequential(
            nn.Linear(512*4*4*4, 4096),
            nn.<PERSON>(True),
            nn.Dropout(p=0.7),
            nn.Linear(4096, 4096),
            nn.ReLU(True),
            nn.Dropout(p=0.7),
            nn.Linear(4096, num_classes)
        )
        if init_weights:
            self._initialize_weights()

    def forward(self, x):
        # N x C x D x H x W (3D MRI)
        x = self.features(x)
        # 自适应池化统一尺寸
        x = self.adaptive_avg_pool(x)
        # 展平特征图
        x = torch.flatten(x, start_dim=1)
        x = self.classifier(x)
        return x

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):  # 修改为Conv3d
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)


def make_features(cfg: list, in_channels=1):  # 默认输入通道为1 (MRI单通道)
    layers = []
    for v in cfg:
        if v == "M":
            # 使用3D最大池化
            layers += [nn.MaxPool3d(kernel_size=2, stride=2)]
        else:
            # 使用3D卷积，padding=1保持空间尺寸
            conv3d = nn.Conv3d(in_channels, v, kernel_size=3, padding=1)
            layers += [conv3d, nn.ReLU(True)]
            in_channels = v
    return nn.Sequential(*layers)


cfgs = {
    'vgg11': [64, 'M', 128, 'M', 256, 256, 'M', 512, 512, 'M', 512, 512, 'M'],
    'vgg13': [64, 64, 'M', 128, 128, 'M', 256, 256, 'M', 512, 512, 'M', 512, 512, 'M'],
    'vgg16': [64, 64, 'M', 128, 128, 'M', 256, 256, 256, 'M', 512, 512, 512, 'M', 512, 512, 512, 'M'],
    'vgg19': [64, 64, 'M', 128, 128, 'M', 256, 256, 256, 256, 'M', 512, 512, 512, 512, 'M', 512, 512, 512, 512, 'M'],
}


def vgg(model_name="vgg11", in_channels=1, **kwargs):  # 增加in_channels参数
    assert model_name in cfgs, "Warning: model number {} not in cfgs dict!".format(model_name)
    cfg = cfgs[model_name]
    # 传入in_channels参数
    model = VGG(make_features(cfg, in_channels=in_channels), **kwargs)
    return model