import torch
import torch.nn as nn
import torch.nn.functional as F


class SimpleCNN3D(nn.Module):
    """
    简单高效的3D CNN模型，专为3D MRI分类设计
    特点：
    - 参数量少，减少过拟合风险
    - 使用BatchNorm和Dropout正则化
    - 渐进式特征提取
    """
    def __init__(self, in_channels=1, num_classes=2, dropout_rate=0.5):
        super(SimpleCNN3D, self).__init__()
        
        # 第一个卷积块：64个特征
        self.conv1 = nn.Conv3d(in_channels, 32, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm3d(32)
        self.pool1 = nn.MaxPool3d(2, stride=2)
        
        # 第二个卷积块：128个特征
        self.conv2 = nn.Conv3d(32, 64, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm3d(64)
        self.pool2 = nn.MaxPool3d(2, stride=2)
        
        # 第三个卷积块：256个特征
        self.conv3 = nn.Conv3d(64, 128, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm3d(128)
        self.pool3 = nn.MaxPool3d(2, stride=2)
        
        # 全局平均池化，大幅减少参数
        self.global_avg_pool = nn.AdaptiveAvgPool3d(1)
        
        # 简化的分类器
        self.classifier = nn.Sequential(
            nn.Dropout(dropout_rate),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate * 0.5),
            nn.Linear(64, num_classes)
        )
        
        self._initialize_weights()
    
    def forward(self, x):
        # 第一个卷积块
        x = F.relu(self.bn1(self.conv1(x)))
        x = self.pool1(x)
        
        # 第二个卷积块
        x = F.relu(self.bn2(self.conv2(x)))
        x = self.pool2(x)
        
        # 第三个卷积块
        x = F.relu(self.bn3(self.conv3(x)))
        x = self.pool3(x)
        
        # 全局平均池化
        x = self.global_avg_pool(x)
        x = torch.flatten(x, 1)
        
        # 分类
        x = self.classifier(x)
        return x
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)


class TinyCNN3D(nn.Module):
    """
    极简3D CNN模型，参数量最少
    """
    def __init__(self, in_channels=1, num_classes=2, dropout_rate=0.6):
        super(TinyCNN3D, self).__init__()
        
        # 只有两个卷积块
        self.conv1 = nn.Conv3d(in_channels, 16, kernel_size=5, padding=2)
        self.bn1 = nn.BatchNorm3d(16)
        self.pool1 = nn.MaxPool3d(4, stride=4)  # 更大的池化
        
        self.conv2 = nn.Conv3d(16, 32, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm3d(32)
        self.pool2 = nn.MaxPool3d(4, stride=4)  # 更大的池化
        
        # 全局平均池化
        self.global_avg_pool = nn.AdaptiveAvgPool3d(1)
        
        # 极简分类器
        self.classifier = nn.Sequential(
            nn.Dropout(dropout_rate),
            nn.Linear(32, num_classes)
        )
        
        self._initialize_weights()
    
    def forward(self, x):
        x = F.relu(self.bn1(self.conv1(x)))
        x = self.pool1(x)
        
        x = F.relu(self.bn2(self.conv2(x)))
        x = self.pool2(x)
        
        x = self.global_avg_pool(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)
        return x
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)


def simple_cnn3d(model_type="simple", **kwargs):
    """
    创建简单的3D CNN模型
    Args:
        model_type: "simple" 或 "tiny"
    """
    if model_type == "simple":
        return SimpleCNN3D(**kwargs)
    elif model_type == "tiny":
        return TinyCNN3D(**kwargs)
    else:
        raise ValueError(f"Unknown model_type: {model_type}")


def count_parameters(model):
    """计算模型参数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


if __name__ == "__main__":
    # 测试模型
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建测试数据
    test_input = torch.randn(2, 1, 64, 64, 64).to(device)
    
    # 测试SimpleCNN3D
    model_simple = simple_cnn3d("simple", num_classes=2).to(device)
    output_simple = model_simple(test_input)
    print(f"SimpleCNN3D - Parameters: {count_parameters(model_simple):,}")
    print(f"SimpleCNN3D - Output shape: {output_simple.shape}")
    
    # 测试TinyCNN3D
    model_tiny = simple_cnn3d("tiny", num_classes=2).to(device)
    output_tiny = model_tiny(test_input)
    print(f"TinyCNN3D - Parameters: {count_parameters(model_tiny):,}")
    print(f"TinyCNN3D - Output shape: {output_tiny.shape}")
    
    # 与VGG11比较
    from vgg3D_model import vgg
    vgg_model = vgg("vgg11", in_channels=1, num_classes=2).to(device)
    print(f"VGG11 - Parameters: {count_parameters(vgg_model):,}")